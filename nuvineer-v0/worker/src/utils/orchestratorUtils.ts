// orchestratorUtils.ts - TypeScript implementation of orchestrator functions

import crypto from 'crypto';

// Import required utilities
import { callClaudeLlmFor<PERSON>son } from './llmUtils';
import { supabaseAdmin } from './supabaseClient';
import {
  storeDecisionRecord,
  getAllDecisionMetadataForRepo as getVectorDecisionMetadata,
} from './vectorOperations';
import {
  PRContext,
  CodeChange,
  Comment,
  ProcessMergedPRResult,
  GetAllDecisionMetadataOptions,
  DecisionMetadata,
} from '../types';
import { logger } from './logger';

/**
 * Extracts installation ID and repository slug from namespace
 * This enables gradual migration from namespace-based calls to installationId/repositorySlug calls
 */
export function extractParamsFromNamespace(namespace: string): {
  installationId: number;
  repositorySlug: string;
} {
  if (!namespace) {
    throw new Error('Namespace is required');
  }

  const parts = namespace.split('-');
  if (parts.length < 3) {
    throw new Error(`Cannot parse namespace format: ${namespace}`);
  }

  const installationId = parseInt(parts[0]);
  if (isNaN(installationId)) {
    throw new Error(`Invalid installation ID in namespace: ${parts[0]}`);
  }

  // Extract repository slug from namespace format: {installationId}-{owner}--{repo}
  const ownerRepoPart = parts.slice(1).join('-');
  const ownerRepoMatch = ownerRepoPart.match(/^(.+)--(.+)$/);

  if (!ownerRepoMatch) {
    throw new Error(
      `Invalid namespace format for repo extraction: ${namespace}`
    );
  }

  const repositorySlug = `${ownerRepoMatch[1]}/${ownerRepoMatch[2]}`;

  return { installationId, repositorySlug };
}

/**
 * Validates a GitHub webhook signature.
 */
export function validateGitHubSignature(
  payload: string,
  signature: string,
  secret: string
): boolean {
  try {
    const expectedSignature = `sha256=${crypto
      .createHmac('sha256', secret)
      .update(payload)
      .digest('hex')}`;

    return crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(expectedSignature)
    );
  } catch (error) {
    logger.error(
      '[orchestratorUtils] Error validating GitHub signature:',
      error
    );
    return false;
  }
}

/**
 * Logs the outcome of processing a PR.
 */
export async function logProcessedPR(
  prContext: PRContext,
  status: string,
  decisionsCount: number,
  errorMessage: string = '',
  decisions: any[] = []
): Promise<void> {
  try {
    logger.info(
      `[ProcessedPR] ${prContext.html_url} - Status: ${status}, Decisions: ${decisionsCount}`
    );

    if (errorMessage) {
      if (status === 'failed' || status === 'completed_with_errors') {
        logger.error(`[ProcessedPR] Error: ${errorMessage}`);
      } else {
        logger.info(`[ProcessedPR] ${errorMessage}`);
      }
    }

    // Log decision details if provided
    if (decisions.length > 0) {
      logger.info(
        `[ProcessedPR] Decision details:`,
        decisions.map(d => ({ title: d.title, id: d.id }))
      );
    }
  } catch (error) {
    logger.error('[orchestratorUtils] Error in logProcessedPR:', error);
  }
}

/**
 * Generates a prompt for LLM analysis of PR data
 * This is a simplified version of the prompt generation logic
 */
function generateAnalysisPrompt(
  prContext: PRContext,
  codeChanges: CodeChange[],
  comments: Comment[],
  deploymentConstitution?: any
): string {
  const prDescription = `
Pull Request Analysis:
- Title: ${prContext.title}
- URL: ${prContext.html_url}
- Body: ${prContext.body || 'No description provided'}
- Merged At: ${prContext.merged_at}
- Author: ${
    typeof prContext.user === 'object' ? prContext.user.login : prContext.user
  }
`;

  const codeChangesDescription =
    codeChanges.length > 0
      ? `\nCode Changes (${codeChanges.length} files):\n` +
        codeChanges
          .map(
            change =>
              `- ${change.filename} (+${change.additions || 0}/-${
                change.deletions || 0
              })\n${change.patch || ''}`
          )
          .join('\n')
      : '\nNo code changes detected.';

  const commentsDescription =
    comments.length > 0
      ? `\nPR Comments (${comments.length} comments):\n` +
        comments
          .map(
            comment => `- ${comment.user?.login || 'Unknown'}: ${comment.body}`
          )
          .join('\n')
      : '\nNo comments found.';

  const constitutionDescription = deploymentConstitution
    ? `\nDeployment Constitution:\n${JSON.stringify(
        deploymentConstitution,
        null,
        2
      )}`
    : '';

  return `${prDescription}${codeChangesDescription}${commentsDescription}${constitutionDescription}

Please analyze this pull request and extract any architectural decisions made. Return a JSON response with the following structure:
{
  "architectural_decisions": [
    {
      "title": "Brief title of the decision",
      "description": "Detailed description of what was decided",
      "rationale": "Why this decision was made",
      "alternatives_considered": "What other options were considered",
      "impact": "Impact of this decision",
      "consequences": "Positive and negative consequences"
    }
  ],
  "no_architecture_impact_reason": "Reason if no architectural decisions were found",
  "no_decision_confidence_score": 0.95
}`;
}

/**
 * Processes a merged Pull Request to extract and analyze architectural decisions.
 * This is a TypeScript implementation with core orchestrator logic.
 */
export async function processMergedPR(
  prContext: PRContext,
  codeChanges: CodeChange[],
  comments: Comment[],
  namespace: string,
  installationRepositorySlug: string,
  designDocContent?: string,
  skipRelationshipAnalysis: boolean = false
): Promise<ProcessMergedPRResult> {
  const startTime = Date.now();
  const logPrefix = `[ProcessMergedPR - ${
    prContext?.number || 'unknown'
  } - ${installationRepositorySlug}]`;

  try {
    // Extract installationId from namespace for modern vector operations
    let installationId: number;
    try {
      const extracted = extractParamsFromNamespace(namespace);
      installationId = extracted.installationId;
      logger.info(
        `${logPrefix} Extracted installationId: ${installationId} from namespace: ${namespace}`
      );
    } catch (error) {
      logger.error(
        `${logPrefix} Failed to extract installationId from namespace: ${namespace}`,
        error
      );
      throw new Error(`Invalid namespace format: ${namespace}`);
    }

    logger.info(
      `[Orchestrator] Starting analysis for PR #${prContext.number} in ${installationRepositorySlug} (isPseudo: ${prContext.is_pseudo_pr}).`
    );
    logger.info(`${logPrefix} Starting deep analysis for ${prContext.title}`);
    logger.info(
      `${logPrefix} Repository: ${installationRepositorySlug}, InstallationId: ${installationId}`
    );
    logger.info(
      `${logPrefix} Code changes: ${codeChanges.length} files, Comments: ${comments.length}`
    );

    // Fetch Deployment Constitution
    let deploymentConstitution = null;
    try {
      if (supabaseAdmin) {
        const { data: constitutionData, error: constitutionError } =
          await supabaseAdmin
            .from('deployment_constitutions')
            .select('constitution_data')
            .eq('repository_slug', installationRepositorySlug)
            .single();

        if (constitutionError) {
          if (constitutionError.code !== 'PGRST116') {
            // PGRST116 = "No rows found"
            logger.warn(
              `${logPrefix} Error fetching deployment constitution:`,
              constitutionError.message
            );
          }
        } else if (constitutionData) {
          deploymentConstitution = constitutionData.constitution_data;
          logger.info(
            `${logPrefix} Successfully loaded Deployment Constitution.`
          );
        }
      }
    } catch (err) {
      logger.error(
        `${logPrefix} Unexpected error fetching deployment constitution:`,
        err
      );
    }

    logger.info(
      `${logPrefix} ==================== DEEP ANALYSIS ====================`
    );
    logger.info(
      `${logPrefix} Beginning comprehensive architectural decision analysis...`
    );

    let decisions = [];
    let decisionsCount = 0;
    let status = 'processing';
    let errorMessage = '';

    // Generate prompt and call LLM
    logger.debug(
      `[Orchestrator] Standard run. Generating ADR extraction prompt.`
    );
    const prompt = generateAnalysisPrompt(
      prContext,
      codeChanges,
      comments,
      deploymentConstitution
    );
    const result = await callClaudeLlmForJson(prompt);

    // Validate the primary response structure
    if (!result || !Array.isArray(result.architectural_decisions)) {
      logger.error(
        `[Orchestrator] Invalid LLM response structure: ${JSON.stringify(
          result
        )}`
      );
      throw new Error(
        `Invalid LLM response structure. Expected 'architectural_decisions' array. Received: ${JSON.stringify(
          result
        )}`
      );
    }

    decisions = result.architectural_decisions || [];
    decisionsCount = decisions.length;
    const noDecisionReason = result.no_architecture_impact_reason || null;

    if (decisionsCount === 0) {
      logger.debug(
        `[Orchestrator] No significant architectural decisions identified. Reason: ${
          noDecisionReason || 'None provided'
        }`
      );
      status = 'completed_no_decisions';
      errorMessage = noDecisionReason;
      await logProcessedPR(prContext, status, decisionsCount, errorMessage);
      return {
        status,
        decisionsCount,
        reason: errorMessage,
        duration: Date.now() - startTime,
      };
    }

    logger.info(
      `[Orchestrator] Extracted ${decisionsCount} potential architectural decision(s).`
    );

    // Process each decision: enrich and prepare for storage
    const processedDecisions = [];
    const processingErrors = [];

    for (let i = 0; i < decisions.length; i++) {
      let decision = decisions[i];
      const decisionNumber = i + 1;
      logger.debug(
        `[Orchestrator] Processing decision ${decisionNumber}/${decisionsCount}: "${decision.title}"`
      );

      try {
        // Add metadata before storage/analysis
        decision.pr_number = prContext.number;
        decision.pr_url = prContext.html_url;
        decision.pr_merged_at = prContext.merged_at
          ? Date.parse(prContext.merged_at)
          : Date.now();
        decision.repository_slug = installationRepositorySlug;
        decision.extracted_at = new Date().toISOString();
        decision.pr_author =
          typeof prContext.user === 'object'
            ? prContext.user.login
            : prContext.user || 'unknown';
        decision.pr_title = prContext.title;
        decision.is_superseded = false;

        // Store the decision in the vector database
        try {
          const vectorId = await storeDecisionRecord(
            decision,
            installationId,
            installationRepositorySlug
          );
          decision.vector_id = vectorId;

          logger.debug(
            `[Orchestrator] Decision "${decision.title}" stored successfully with vector ID: ${vectorId}`
          );
        } catch (storageError) {
          logger.error(
            `[Orchestrator] Error storing decision "${decision.title}" in vector database:`,
            storageError
          );
          // Continue processing even if storage fails - the decision is still valid
          logger.debug(
            `[Orchestrator] Decision "${decision.title}" processed successfully (storage failed but continuing)`
          );
        }

        processedDecisions.push(decision);
      } catch (decisionError) {
        logger.error(
          `[Orchestrator] Error processing decision "${decision.title}":`,
          decisionError
        );
        processingErrors.push({
          title: decision.title,
          error:
            decisionError instanceof Error
              ? decisionError.message
              : String(decisionError),
        });
      }
    }

    // Determine final status based on processing errors
    if (processingErrors.length > 0) {
      if (processingErrors.length === decisionsCount) {
        status = 'failed';
        errorMessage = `All ${decisionsCount} decisions failed processing. See logs for details. First error: ${processingErrors[0].error}`;
      } else {
        status = 'completed_with_errors';
        errorMessage = `${
          processingErrors.length
        } out of ${decisionsCount} decisions failed processing. See logs. Errors: ${JSON.stringify(
          processingErrors
        )}`;
      }
    } else {
      status = 'completed_successfully';
      errorMessage = `Successfully processed ${processedDecisions.length} decision(s).`;
    }

    logger.info(
      `[Orchestrator] Finished processing PR ${prContext.html_url}. Status: ${status}`
    );
    await logProcessedPR(
      prContext,
      status,
      processedDecisions.length,
      errorMessage,
      processedDecisions
    );

    return {
      status,
      decisions: processedDecisions,
      duration: Date.now() - startTime,
      errors: processingErrors,
    };
  } catch (error) {
    logger.error(
      `[Orchestrator] Unhandled error during PR processing for ${prContext.html_url}:`,
      error
    );
    const status = 'failed';
    const errorMessage =
      error instanceof Error
        ? error.message
        : 'An unknown error occurred during processing.';
    await logProcessedPR(prContext, status, 0, errorMessage);
    return {
      status,
      error: errorMessage,
      decisions: [],
      duration: Date.now() - startTime,
    };
  }
}

/**
 * Retrieves all decision metadata for a given repository.
 * Uses the vector operations to get decisions from the vector database.
 */
export async function getAllDecisionMetadataForRepo(
  installationId: number,
  repositorySlug: string,
  options: GetAllDecisionMetadataOptions = {}
): Promise<DecisionMetadata[]> {
  const logPrefix = `[Orchestrator getAllDecisionMetadata - Repo: ${repositorySlug}]`;
  logger.debug(`${logPrefix} Fetching all decisions`);

  if (typeof installationId !== 'number' || !repositorySlug) {
    logger.error(`${logPrefix} Missing required parameters.`);
    return [];
  }

  try {
    // Use the vector operations to get decision metadata
    const decisions = await getVectorDecisionMetadata(
      installationId,
      repositorySlug,
      options
    );
    logger.debug(
      `${logPrefix} Retrieved ${decisions.length} decisions from vector database`
    );
    return decisions;
  } catch (error) {
    logger.error(`${logPrefix} Error fetching decision metadata:`, error);
    return [];
  }
}
