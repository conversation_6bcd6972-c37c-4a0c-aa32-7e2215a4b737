// orchestratorUtils.ts - TypeScript implementation of orchestrator functions

import crypto from 'crypto';

// Import required utilities
import { callClaudeLlmFor<PERSON>son } from './llmUtils';
import { supabaseAdmin } from './supabaseClient';
import {
  storeDecisionRecord,
  getAllDecisionMetadataForRepo as getVectorDecisionMetadata,
  findDecisionsByDomainConcepts,
  findDecisionsByFileOverlap,
  findDecisionsByImpactOverlap,
  markDecisionAsSuperseded,
} from './vectorOperations';
import {
  PRContext,
  CodeChange,
  Comment,
  ProcessMergedPRResult,
  GetAllDecisionMetadataOptions,
  DecisionMetadata,
} from '../types';
import { logger } from './logger';
import { generatePrompt } from './prompt';

/**
 * Extracts installation ID and repository slug from namespace
 * This enables gradual migration from namespace-based calls to installationId/repositorySlug calls
 */
export function extractParamsFromNamespace(namespace: string): {
  installationId: number;
  repositorySlug: string;
} {
  if (!namespace) {
    throw new Error('Namespace is required');
  }

  const parts = namespace.split('-');
  if (parts.length < 3) {
    throw new Error(`Cannot parse namespace format: ${namespace}`);
  }

  const installationId = parseInt(parts[0]);
  if (isNaN(installationId)) {
    throw new Error(`Invalid installation ID in namespace: ${parts[0]}`);
  }

  // Extract repository slug from namespace format: {installationId}-{owner}--{repo}
  const ownerRepoPart = parts.slice(1).join('-');
  const ownerRepoMatch = ownerRepoPart.match(/^(.+)--(.+)$/);

  if (!ownerRepoMatch) {
    throw new Error(
      `Invalid namespace format for repo extraction: ${namespace}`
    );
  }

  const repositorySlug = `${ownerRepoMatch[1]}/${ownerRepoMatch[2]}`;

  return { installationId, repositorySlug };
}

/**
 * Validates a GitHub webhook signature.
 */
export function validateGitHubSignature(
  payload: string,
  signature: string,
  secret: string
): boolean {
  try {
    const expectedSignature = `sha256=${crypto
      .createHmac('sha256', secret)
      .update(payload)
      .digest('hex')}`;

    return crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(expectedSignature)
    );
  } catch (error) {
    logger.error(
      '[orchestratorUtils] Error validating GitHub signature:',
      error
    );
    return false;
  }
}

/**
 * Logs the outcome of processing a PR.
 */
export async function logProcessedPR(
  prContext: PRContext,
  status: string,
  decisionsCount: number,
  errorMessage: string = '',
  decisions: any[] = []
): Promise<void> {
  try {
    logger.info(
      `[ProcessedPR] ${prContext.html_url} - Status: ${status}, Decisions: ${decisionsCount}`
    );

    if (errorMessage) {
      if (status === 'failed' || status === 'completed_with_errors') {
        logger.error(`[ProcessedPR] Error: ${errorMessage}`);
      } else {
        logger.info(`[ProcessedPR] ${errorMessage}`);
      }
    }

    // Log decision details if provided
    if (decisions.length > 0) {
      logger.info(
        `[ProcessedPR] Decision details:`,
        decisions.map(d => ({ title: d.title, id: d.id }))
      );
    }
  } catch (error) {
    logger.error('[orchestratorUtils] Error in logProcessedPR:', error);
  }
}

/**
 * Format code changes for the prompt
 */
function formatCodeChanges(codeChanges: CodeChange[]): string {
  if (!codeChanges || codeChanges.length === 0) {
    return 'No code changes provided or format is invalid.';
  }

  const MAX_FILES_TO_SHOW = 5;
  const MAX_PATCH_LINES = 50;

  const relevantChanges = codeChanges.slice(0, MAX_FILES_TO_SHOW);
  let result = 'Key code changes:\n\n';

  for (const file of relevantChanges) {
    result += `File: ${file.filename || 'Unknown File'}\n`;

    if (file.patch) {
      const patchLines = file.patch.split('\n');
      const truncatedPatch = patchLines.slice(0, MAX_PATCH_LINES).join('\n');
      result += 'Diff (potentially truncated):\n```diff\n';
      result += truncatedPatch;
      if (patchLines.length > MAX_PATCH_LINES) {
        result += '\n... (truncated for brevity)';
      }
      result += '\n```\n\n';
    } else {
      result += 'No patch available\n\n';
    }
  }

  if (codeChanges.length > MAX_FILES_TO_SHOW) {
    result += `... and ${codeChanges.length - MAX_FILES_TO_SHOW} more files\n`;
  }

  return result;
}

/**
 * Processes a merged Pull Request to extract and analyze architectural decisions.
 * This is a TypeScript implementation with core orchestrator logic.
 */
export async function processMergedPR(
  prContext: PRContext,
  codeChanges: CodeChange[],
  comments: Comment[],
  namespace: string,
  installationRepositorySlug: string,
  designDocContent?: string,
  skipRelationshipAnalysis: boolean = false
): Promise<ProcessMergedPRResult> {
  const startTime = Date.now();
  const logPrefix = `[ProcessMergedPR - ${
    prContext?.number || 'unknown'
  } - ${installationRepositorySlug}]`;

  try {
    // Extract installationId from namespace for modern vector operations
    let installationId: number;
    try {
      const extracted = extractParamsFromNamespace(namespace);
      installationId = extracted.installationId;
      logger.info(
        `${logPrefix} Extracted installationId: ${installationId} from namespace: ${namespace}`
      );
    } catch (error) {
      logger.error(
        `${logPrefix} Failed to extract installationId from namespace: ${namespace}`,
        error
      );
      throw new Error(`Invalid namespace format: ${namespace}`);
    }

    logger.info(
      `[Orchestrator] Starting analysis for PR #${prContext.number} in ${installationRepositorySlug} (isPseudo: ${prContext.is_pseudo_pr}).`
    );
    logger.info(`${logPrefix} Starting deep analysis for ${prContext.title}`);
    logger.info(
      `${logPrefix} Repository: ${installationRepositorySlug}, InstallationId: ${installationId}`
    );
    logger.info(
      `${logPrefix} Code changes: ${codeChanges.length} files, Comments: ${comments.length}`
    );

    // Fetch Deployment Constitution
    let deploymentConstitution = null;
    try {
      if (supabaseAdmin) {
        const { data: constitutionData, error: constitutionError } =
          await supabaseAdmin
            .from('deployment_constitutions')
            .select('constitution_data')
            .eq('repository_slug', installationRepositorySlug)
            .single();

        if (constitutionError) {
          if (constitutionError.code !== 'PGRST116') {
            // PGRST116 = "No rows found"
            logger.warn(
              `${logPrefix} Error fetching deployment constitution:`,
              constitutionError.message
            );
          }
        } else if (constitutionData) {
          deploymentConstitution = constitutionData.constitution_data;
          logger.info(
            `${logPrefix} Successfully loaded Deployment Constitution.`
          );
        }
      }
    } catch (err) {
      logger.error(
        `${logPrefix} Unexpected error fetching deployment constitution:`,
        err
      );
    }

    logger.info(
      `${logPrefix} ==================== DEEP ANALYSIS ====================`
    );
    logger.info(
      `${logPrefix} Beginning comprehensive architectural decision analysis...`
    );

    let decisions = [];
    let decisionsCount = 0;
    let status = 'processing';
    let errorMessage = '';

    // Generate prompt and call LLM
    logger.debug(
      `[Orchestrator] Standard run. Generating ADR extraction prompt.`
    );
    const prompt = generatePrompt(
      prContext,
      codeChanges,
      comments,
      deploymentConstitution
    );
    const result = await callClaudeLlmForJson(
      prompt,
      'claude-sonnet-4-20250514'
    );

    // Validate the primary response structure
    if (!result || !Array.isArray(result.architectural_decisions)) {
      logger.error(
        `[Orchestrator] Invalid LLM response structure: ${JSON.stringify(
          result
        )}`
      );
      throw new Error(
        `Invalid LLM response structure. Expected 'architectural_decisions' array. Received: ${JSON.stringify(
          result
        )}`
      );
    }

    decisions = result.architectural_decisions || [];
    decisionsCount = decisions.length;
    const noDecisionReason = result.no_architecture_impact_reason || null;

    if (decisionsCount === 0) {
      logger.debug(
        `[Orchestrator] No significant architectural decisions identified. Reason: ${
          noDecisionReason || 'None provided'
        }`
      );
      status = 'completed_no_decisions';
      errorMessage = noDecisionReason;
      await logProcessedPR(prContext, status, decisionsCount, errorMessage);
      return {
        status,
        decisionsCount,
        reason: errorMessage,
        duration: Date.now() - startTime,
      };
    }

    logger.info(
      `[Orchestrator] Extracted ${decisionsCount} potential architectural decision(s).`
    );

    // Process each decision: enrich and prepare for storage
    const processedDecisions = [];
    const processingErrors = [];

    for (let i = 0; i < decisions.length; i++) {
      let decision = decisions[i];
      const decisionNumber = i + 1;
      logger.debug(
        `[Orchestrator] Processing decision ${decisionNumber}/${decisionsCount}: "${decision.title}"`
      );

      try {
        // Add metadata before storage/analysis
        decision.pr_number = prContext.number;
        decision.pr_url = prContext.html_url;
        decision.pr_merged_at = prContext.merged_at
          ? Date.parse(prContext.merged_at)
          : Date.now();
        decision.repository_slug = installationRepositorySlug;
        decision.extracted_at = new Date().toISOString();
        decision.pr_author =
          typeof prContext.user === 'object'
            ? prContext.user.login
            : prContext.user || 'unknown';
        decision.pr_title = prContext.title;
        decision.is_superseded = false;

        // Ensure field mapping consistency with main environment
        // Map risks_extracted to risks for consistency
        if (decision.risks_extracted && !decision.risks) {
          decision.risks = decision.risks_extracted;
        }

        // Ensure related_files is properly formatted
        if (
          decision.related_files &&
          typeof decision.related_files === 'string'
        ) {
          decision.related_files = decision.related_files
            .split(',')
            .map((f: string) => f.trim());
        }

        // Ensure domain_concepts is properly formatted
        if (
          decision.domain_concepts &&
          typeof decision.domain_concepts === 'string'
        ) {
          decision.domain_concepts = decision.domain_concepts
            .split(',')
            .map((c: string) => c.trim());
        }

        // Map implications field (main environment uses 'implications', worker might have 'impact')
        if (decision.impact && !decision.implications) {
          decision.implications = decision.impact;
        }

        // Ensure confidence_score is present
        if (typeof decision.confidence_score !== 'number') {
          decision.confidence_score = 0.8; // Default confidence
        }

        // Ensure boolean fields are properly set
        if (typeof decision.is_extension !== 'boolean') {
          decision.is_extension = false;
        }

        if (typeof decision.follows_standard_practice !== 'boolean') {
          decision.follows_standard_practice = false;
        }

        // --- Relationship Analysis (Forward-Looking) ---
        logger.debug(
          `[Orchestrator] Performing forward-looking relationship analysis for "${decision.title}".`
        );
        try {
          const namespace = `${installationId}_${installationRepositorySlug}`;
          const relationshipAnalysis = await analyzeRelationships(
            decision,
            namespace,
            { direction: 'forward' }
          );
          const supersedingRelationship =
            relationshipAnalysis.relationships?.find(
              (rel: any) => rel.relationship_type === 'supersedes'
            );

          if (supersedingRelationship) {
            decision.is_superseded = true;
            decision.superseded_by_decision_id =
              supersedingRelationship.existing_decision_id;
            logger.debug(
              `[Orchestrator] Decision "${decision.title}" marked as superseded by ${supersedingRelationship.existing_decision_id}.`
            );
          } else {
            decision.is_superseded = false;
          }
          decision.relationships_analysis =
            JSON.stringify(relationshipAnalysis); // Store for debugging/future use
        } catch (relationshipError) {
          logger.warn(
            `[Orchestrator] Error during relationship analysis for "${decision.title}":`,
            relationshipError
          );
          // Continue without relationship analysis if it fails
          decision.relationships_analysis = JSON.stringify({
            relationships: [],
            potential_matches_for_analysis: [],
            error:
              relationshipError instanceof Error
                ? relationshipError.message
                : 'Unknown error',
          });
        }

        // Store the decision in the vector database
        try {
          const vectorId = await storeDecisionRecord(
            decision,
            installationId,
            installationRepositorySlug
          );
          decision.vector_id = vectorId;

          logger.debug(
            `[Orchestrator] Decision "${decision.title}" stored successfully with vector ID: ${vectorId}`
          );

          // Handle superseding relationships if any were identified
          if (decision.relationships_analysis) {
            try {
              const relationshipData = JSON.parse(
                decision.relationships_analysis
              );
              if (relationshipData.relationships) {
                const currentDecisionSupersedes =
                  relationshipData.relationships.filter(
                    (rel: any) => rel.relationship_type === 'supersedes'
                  );
                if (currentDecisionSupersedes.length > 0) {
                  logger.debug(
                    `[Orchestrator] Decision "${decision.title}" (${vectorId}) supersedes ${currentDecisionSupersedes.length} other decision(s). Marking them in vector database.`
                  );
                  for (const rel of currentDecisionSupersedes) {
                    await markDecisionAsSuperseded(
                      rel.existing_decision_id,
                      vectorId,
                      installationId,
                      installationRepositorySlug
                    );
                  }
                }
              }
            } catch (relationshipProcessingError) {
              logger.warn(
                `[Orchestrator] Error processing superseding relationships for "${decision.title}":`,
                relationshipProcessingError
              );
            }
          }
        } catch (storageError) {
          logger.error(
            `[Orchestrator] Error storing decision "${decision.title}" in vector database:`,
            storageError
          );
          // Continue processing even if storage fails - the decision is still valid
          logger.debug(
            `[Orchestrator] Decision "${decision.title}" processed successfully (storage failed but continuing)`
          );
        }

        processedDecisions.push(decision);
      } catch (decisionError) {
        logger.error(
          `[Orchestrator] Error processing decision "${decision.title}":`,
          decisionError
        );
        processingErrors.push({
          title: decision.title,
          error:
            decisionError instanceof Error
              ? decisionError.message
              : String(decisionError),
        });
      }
    }

    // Determine final status based on processing errors
    if (processingErrors.length > 0) {
      if (processingErrors.length === decisionsCount) {
        status = 'failed';
        errorMessage = `All ${decisionsCount} decisions failed processing. See logs for details. First error: ${processingErrors[0].error}`;
      } else {
        status = 'completed_with_errors';
        errorMessage = `${
          processingErrors.length
        } out of ${decisionsCount} decisions failed processing. See logs. Errors: ${JSON.stringify(
          processingErrors
        )}`;
      }
    } else {
      status = 'completed_successfully';
      errorMessage = `Successfully processed ${processedDecisions.length} decision(s).`;
    }

    logger.info(
      `[Orchestrator] Finished processing PR ${prContext.html_url}. Status: ${status}`
    );
    await logProcessedPR(
      prContext,
      status,
      processedDecisions.length,
      errorMessage,
      processedDecisions
    );

    return {
      status,
      decisions: processedDecisions,
      duration: Date.now() - startTime,
      errors: processingErrors,
    };
  } catch (error) {
    logger.error(
      `[Orchestrator] Unhandled error during PR processing for ${prContext.html_url}:`,
      error
    );
    const status = 'failed';
    const errorMessage =
      error instanceof Error
        ? error.message
        : 'An unknown error occurred during processing.';
    await logProcessedPR(prContext, status, 0, errorMessage);
    return {
      status,
      error: errorMessage,
      decisions: [],
      duration: Date.now() - startTime,
    };
  }
}

/**
 * Retrieves all decision metadata for a given repository.
 * Uses the vector operations to get decisions from the vector database.
 */
export async function getAllDecisionMetadataForRepo(
  installationId: number,
  repositorySlug: string,
  options: GetAllDecisionMetadataOptions = {}
): Promise<DecisionMetadata[]> {
  const logPrefix = `[Orchestrator getAllDecisionMetadata - Repo: ${repositorySlug}]`;
  logger.debug(`${logPrefix} Fetching all decisions`);

  if (typeof installationId !== 'number' || !repositorySlug) {
    logger.error(`${logPrefix} Missing required parameters.`);
    return [];
  }

  try {
    // Use the vector operations to get decision metadata
    const decisions = await getVectorDecisionMetadata(
      installationId,
      repositorySlug,
      options
    );
    logger.debug(
      `${logPrefix} Retrieved ${decisions.length} decisions from vector database`
    );
    return decisions;
  } catch (error) {
    logger.error(`${logPrefix} Error fetching decision metadata:`, error);
    return [];
  }
}

/**
 * Analyze relationships between decisions
 * Ported from main orchestrator to ensure metadata consistency
 */
export async function analyzeRelationships(
  decision: any,
  targetNamespace: string,
  options: { direction?: string } = {}
): Promise<{
  relationships: any[];
  potential_matches_for_analysis: any[];
  error?: string;
}> {
  const { direction = 'backward' } = options;

  if (!targetNamespace) {
    logger.error(
      `[analyzeRelationships - Dec: "${decision.title}"] CRITICAL: targetNamespace not provided. Aborting analysis.`
    );
    return {
      relationships: [],
      potential_matches_for_analysis: [],
      error: 'targetNamespace is required for analyzeRelationships.',
    };
  }

  logger.debug(
    `[Orchestrator] Analyzing relationships for decision "${decision.title}" in namespace ${targetNamespace}.`
  );

  try {
    // CRITICAL: Get the current decision's merge time to enforce chronological constraint
    const currentDecisionMergeTime = decision.pr_merged_at;
    if (!currentDecisionMergeTime) {
      logger.error(
        `[Relationship Analysis] CRITICAL: Decision "${decision.title}" has no pr_merged_at timestamp. Cannot enforce chronological constraint.`
      );
      return {
        relationships: [],
        potential_matches_for_analysis: [],
        error: 'Missing pr_merged_at timestamp for chronological constraint.',
      };
    }

    // Convert to timestamp if it's a string
    const currentTimestamp =
      typeof currentDecisionMergeTime === 'string'
        ? new Date(currentDecisionMergeTime).getTime()
        : currentDecisionMergeTime;

    logger.debug(
      `[Relationship Analysis] Current decision "${decision.title}" merge timestamp: ${currentTimestamp} (${new Date(
        currentTimestamp
      ).toISOString()})`
    );

    // Extract installation ID and repository slug from namespace
    const { installationId, repositorySlug } =
      extractParamsFromNamespace(targetNamespace);

    // 1. Find potentially related decisions (by concepts, file overlap, etc.)
    const relatedByConcept = await findDecisionsByDomainConcepts(
      decision.domain_concepts || [],
      installationId,
      repositorySlug
    );
    const relatedByFile = await findDecisionsByFileOverlap(
      decision.related_files || [],
      installationId,
      repositorySlug
    );
    const relatedByImpact = await findDecisionsByImpactOverlap(
      decision.implications || '',
      installationId,
      repositorySlug
    );

    // Combine and deduplicate potential matches based on ID, prioritizing by relevance
    const potentialMatchesMap = new Map();

    // Add all matches with their source type for debugging
    [...relatedByConcept, ...relatedByFile, ...relatedByImpact].forEach(
      match => {
        if (!potentialMatchesMap.has(match.id)) {
          potentialMatchesMap.set(match.id, {
            ...match,
            match_sources: [],
          });
        }
        const existing = potentialMatchesMap.get(match.id);
        if (relatedByConcept.includes(match))
          existing.match_sources.push('concept');
        if (relatedByFile.includes(match)) existing.match_sources.push('file');
        if (relatedByImpact.includes(match))
          existing.match_sources.push('impact');
      }
    );

    let potentialMatches = Array.from(potentialMatchesMap.values());

    // Filter out the current decision itself if it has an ID
    const decisionIdToExclude =
      decision.id || decision.pinecone_id || decision.vector_id;
    if (decisionIdToExclude) {
      potentialMatches = potentialMatches.filter(
        match => match.id !== decisionIdToExclude
      );
    }

    // Apply chronological filtering based on direction
    const finalCandidates = potentialMatches.filter(match => {
      const matchTimestamp = match.metadata?.pr_merged_at;
      if (!matchTimestamp) return false;

      const matchTime =
        typeof matchTimestamp === 'string'
          ? new Date(matchTimestamp).getTime()
          : matchTimestamp;

      if (direction === 'forward') {
        // For forward analysis, only consider decisions that came BEFORE the current one
        return matchTime < currentTimestamp;
      } else {
        // For backward analysis, only consider decisions that came AFTER the current one
        return matchTime > currentTimestamp;
      }
    });

    logger.debug(
      `[Relationship Analysis] Found ${finalCandidates.length} potentially related decisions for "${decision.title}" for direction '${direction}'.`
    );

    // For now, return empty relationships but include the potential matches
    // In a full implementation, this would call an LLM to analyze relationships
    // But to maintain consistency with existing metadata structure, we'll return empty relationships
    return {
      relationships: [],
      potential_matches_for_analysis: finalCandidates,
    };
  } catch (error) {
    logger.error(
      `[Relationship Analysis] Error during relationship analysis for "${decision.title}":`,
      error
    );
    return {
      relationships: [],
      potential_matches_for_analysis: [],
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}
